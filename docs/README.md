# TIBCO BW to Spring Boot CLI Tool

一个强大的 CLI 工具，用于将 TIBCO BW 的 XSD Schema 文件转换为 Spring Boot Java 类。

## 功能特性

✅ **XSD 解析**: 支持复杂的 XSD schema 解析  
✅ **Java 代码生成**: 生成现代化的 Spring Boot Java 类  
✅ **多种注解支持**: Lombok、Jackson、JSR-303 验证注解  
✅ **差异对比**: 对比生成的类与现有 Java 实现的差异  
✅ **类型映射**: 智能的 XSD 到 Java 类型映射  
✅ **嵌套类型**: 支持复杂嵌套类型和数组字段  
✅ **命令行界面**: 友好的交互式命令行体验  

## 安装

```bash
npm install -g tibco-bw-to-springboot-cli
```

或者克隆项目本地安装：

```bash
git clone <repository-url>
cd tibco-movie-example
npm install
npm run build
npm link
```

## 使用方法

### 基本用法

```bash
# 生成 Java 类
tibco-convert generate -i ./schemas -o ./output

# 对比现有实现
tibco-convert compare -i ./schemas -j ./src/main/java/models

# 分析 XSD 结构
tibco-convert analyze -i ./schemas
```

### 命令详解

#### 1. generate - 生成 Java 类

将 XSD schema 转换为 Java 类：

```bash
tibco-convert generate [options]

选项:
  -i, --input <directory>     XSD 文件目录 (必需)
  -o, --output <directory>    输出目录 (默认: ./output)
  -p, --package <package>     Java 包名 (默认: com.example.model)
  --lombok                    启用 Lombok 注解 (默认: true)
  --jackson                   启用 Jackson 注解 (默认: true)
  --validation                启用 JSR-303 验证注解 (默认: true)
  --getters-setters          生成 getter/setter 方法 (默认: false，使用 Lombok 时)
  --constructors             生成构造函数 (默认: false，使用 Lombok 时)
  --toString                 生成 toString 方法 (默认: false，使用 Lombok 时)
```

#### 2. compare - 对比现有实现

对比生成的类与现有 Java 实现：

```bash
tibco-convert compare [options]

选项:
  -i, --input <directory>     XSD 文件目录 (必需)
  -j, --java <directory>      现有 Java 源码目录 (必需)
  -o, --output <file>         对比报告输出文件 (可选)
```

#### 3. analyze - 分析 XSD 结构

分析和展示 XSD 文件的结构信息：

```bash
tibco-convert analyze [options]

选项:
  -i, --input <directory>     XSD 文件目录 (必需)
```

### 交互式模式

如果不提供必需参数，工具会进入交互式模式：

```bash
tibco-convert generate
# 会提示输入目录、输出目录、包名等信息
```

## 配置选项

### Java 生成选项

```typescript
interface JavaGenerationOptions {
  packageName: string;        // Java 包名
  outputDirectory: string;    // 输出目录
  useLombok: boolean;        // 使用 Lombok 注解
  useJacksonAnnotations: boolean;  // 使用 Jackson 注解
  useJSR303Validation: boolean;    // 使用 JSR-303 验证注解
  includeGettersSetters: boolean;  // 生成 getter/setter
  includeConstructors: boolean;    // 生成构造函数
  includeToString: boolean;        // 生成 toString 方法
}
```

### 支持的 XSD 特性

- ✅ Complex Types (复杂类型)
- ✅ Simple Types (简单类型)
- ✅ Elements (元素)
- ✅ Attributes (属性)
- ✅ Nested Types (嵌套类型)
- ✅ Array/List Types (数组/列表类型)
- ✅ Optional Fields (可选字段)
- ✅ Namespaces (命名空间)
- ✅ Type Restrictions (类型限制)

### 类型映射

| XSD 类型 | Java 类型 |
|---------|----------|
| xs:string | String |
| xs:int / xs:integer | Integer |
| xs:long | Long |
| xs:double | Double |
| xs:float | Float |
| xs:boolean | Boolean |
| xs:date | LocalDate |
| xs:dateTime | LocalDateTime |
| xs:time | LocalTime |
| xs:decimal | BigDecimal |

## 示例

### 项目结构

```
your-project/
├── schemas/                 # XSD 文件目录
│   ├── User.xsd
│   ├── Product.xsd
│   └── Order.xsd
├── output/                  # 生成的 Java 类
│   └── com/example/model/
│       ├── User.java
│       ├── Product.java
│       └── Order.java
└── src/main/java/          # 现有 Java 代码（用于对比）
```

### XSD 示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://example.com/user"
           elementFormDefault="qualified">

  <xs:complexType name="User">
    <xs:sequence>
      <xs:element name="id" type="xs:long"/>
      <xs:element name="name" type="xs:string"/>
      <xs:element name="email" type="xs:string" minOccurs="0"/>
      <xs:element name="addresses" type="Address" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="Address">
    <xs:sequence>
      <xs:element name="street" type="xs:string"/>
      <xs:element name="city" type="xs:string"/>
      <xs:element name="zipCode" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
```

### 生成的 Java 类

```java
package com.example.model;

import com.fasterxml.jackson.annotation.*;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.*;
import lombok.*;

/**
 * Generated from XSD schema
 *
 * Complex Type
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class User {

    @NotNull
    @JsonProperty("id")
    private Long id;

    @NotNull
    @NotBlank
    @JsonProperty("name")
    private String name;

    @JsonProperty("email")
    private String email;

    @NotNull
    @Valid
    @JsonProperty("addresses")
    private List<Address> addresses = new ArrayList<>();

}
```

## 开发指南

### 项目结构

```
src/
├── cli/                    # CLI 命令处理
├── parsers/               # XSD 解析器
├── generators/            # Java 代码生成器
├── comparators/           # 代码对比器
├── types/                 # TypeScript 类型定义
└── utils/                 # 工具函数
```

### 运行测试

```bash
npm test                   # 运行所有测试
npm run test:watch         # 监听模式
```

### 构建项目

```bash
npm run build             # 编译 TypeScript
npm run clean            # 清理构建文件
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## License

MIT License - 详见 [LICENSE](LICENSE) 文件

## 更新日志

### v1.0.0
- ✅ 初始版本发布
- ✅ 支持基本 XSD 到 Java 转换
- ✅ 支持 Lombok、Jackson、JSR-303 注解
- ✅ 命令行界面
- ✅ 代码对比功能
- ✅ 完整的单元测试覆盖

## 支持

如果您遇到问题或有功能建议，请在 [GitHub Issues](issues) 中提出。
